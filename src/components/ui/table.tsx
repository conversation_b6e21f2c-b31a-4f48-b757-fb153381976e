"use client";

import * as React from "react";

import { cn } from "~/lib/utils";

interface TableProps extends React.ComponentProps<"table"> {
  containerClassName?: string;
}

function Table({ className, containerClassName, ...props }: TableProps) {
  return (
    <div
      data-slot="table-container"
      className={cn(
        "data-table relative w-full overflow-x-auto rounded-md",
        containerClassName
      )}
    >
      <table
        data-slot="table"
        className={cn("w-full caption-bottom text-sm", className)}
        {...props}
      />
    </div>
  );
}

function TableHeader({ className, ...props }: React.ComponentProps<"thead">) {
  return (
    <thead
      data-slot="table-header"
      className={cn(
        "bg-muted overflow-hidden rounded-md shadow-sm [&_tr:first-child_th:first-child]:rounded-l-md [&_tr:first-child_th:last-child]:rounded-r-md [&_tr:last-child_th:first-child]:rounded-bl-md [&_tr:last-child_th:last-child]:rounded-br-md",
        className
      )}
      {...props}
    />
  );
}

function TableBody({ className, ...props }: React.ComponentProps<"tbody">) {
  return (
    <tbody
      data-slot="table-body"
      className={cn("[&_tr:hover]:bg-muted/50", className)}
      {...props}
    />
  );
}

function TableFooter({ className, ...props }: React.ComponentProps<"tfoot">) {
  return (
    <tfoot
      data-slot="table-footer"
      className={cn("bg-muted/50 font-medium", className)}
      {...props}
    />
  );
}

function TableRow({ className, ...props }: React.ComponentProps<"tr">) {
  return (
    <tr
      data-slot="table-row"
      className={cn(
        "data-[state=selected]:bg-muted hover:bg-muted/50 dark:hover:bg-muted/30 transition-colors [&:hover_td:first-child]:rounded-l-md [&:hover_td:last-child]:rounded-r-md",
        className
      )}
      {...props}
    />
  );
}

interface TableHeadProps extends React.ComponentProps<"th"> {
  isOutput?: boolean;
  isFirstOutput?: boolean;
}

function TableHead({
  className,
  isOutput,
  isFirstOutput,
  ...props
}: TableHeadProps) {
  return (
    <th
      data-slot="table-head"
      data-output={isOutput ? "true" : undefined}
      className={cn(
        "text-muted-foreground h-10 px-4 py-3 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        isOutput && "bg-purple-50/50 dark:bg-purple-900/10",
        isFirstOutput && "border-l-2 border-purple-200 dark:border-purple-800",
        className
      )}
      {...props}
    />
  );
}

interface TableCellProps extends React.ComponentProps<"td"> {
  isOutput?: boolean;
  isFirstOutput?: boolean;
}

function TableCell({
  className,
  isOutput,
  isFirstOutput,
  ...props
}: TableCellProps) {
  return (
    <td
      data-slot="table-cell"
      data-output={isOutput ? "true" : undefined}
      className={cn(
        "px-4 py-3 align-middle text-sm whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        isOutput && "bg-purple-50/50 dark:bg-purple-900/10",
        isFirstOutput && "border-l-2 border-purple-200 dark:border-purple-800",
        className
      )}
      {...props}
    />
  );
}

function TableCaption({
  className,
  ...props
}: React.ComponentProps<"caption">) {
  return (
    <caption
      data-slot="table-caption"
      className={cn("text-muted-foreground mt-4 text-sm", className)}
      {...props}
    />
  );
}

export {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
};
