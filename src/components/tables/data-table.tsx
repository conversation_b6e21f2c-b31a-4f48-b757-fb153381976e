"use client";

import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  Row,
  SortingState,
  useReactTable,
  VisibilityState,
  type Table as ReactTable,
} from "@tanstack/react-table";
import {
  CheckCircleIcon,
  ChevronDownIcon,
  ChevronsUpDownIcon,
  ChevronUpIcon,
  XIcon,
} from "lucide-react";
import * as React from "react";
import { Button } from "~/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { cn } from "~/lib/utils";
import { ColumnVisibilityDropdown } from "./column-visibility-dropdown";

export interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  outputColumnStartIndex?: number;
  hideToolbar?: boolean;
  table?: ReactTable<TData>;
  onRowClick?: (row: Row<TData>) => void;
  containerClassName?: string;
  className?: string;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  outputColumnStartIndex,
  hideToolbar = false,
  table: externalTable,
  onRowClick,
  className,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});

  const internalTable = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
  });

  const table = externalTable ?? internalTable;

  return (
    <div className={cn("flex h-full w-full flex-col", className)}>
      {!hideToolbar && (
        <div className="mb-4 flex items-center justify-between">
          <div className="flex flex-1 items-center space-x-2">
            {/* Add other toolbar items here */}
          </div>
          <ColumnVisibilityDropdown table={table} />
        </div>
      )}
      <div className="min-h-0 flex-1">
        <Table
          className="data-table relative"
          containerClassName="h-full overflow-y-auto"
        >
          <TableHeader className="sticky top-0 z-20">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header, index) => {
                  const isOutput =
                    outputColumnStartIndex !== undefined &&
                    index >= outputColumnStartIndex;
                  const isFirstOutput =
                    outputColumnStartIndex !== undefined &&
                    index === outputColumnStartIndex;
                  return (
                    <TableHead
                      key={header.id}
                      isOutput={isOutput}
                      isFirstOutput={isFirstOutput}
                    >
                      {header.isPlaceholder ? null : (
                        <div
                          onClick={header.column.getToggleSortingHandler()}
                          className={cn(
                            "flex items-center",
                            header.column.getCanSort()
                              ? "hover:bg-accent/50 -mx-4 -my-2 cursor-pointer rounded-md px-4 py-2"
                              : "",
                            !header.column.getCanSort() && "cursor-default"
                          )}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {header.column.getCanSort() && (
                            <span className="ml-2">
                              {{
                                asc: <ChevronUpIcon className="h-4 w-4" />,
                                desc: <ChevronDownIcon className="h-4 w-4" />,
                              }[header.column.getIsSorted() as string] ?? (
                                <ChevronsUpDownIcon className="text-muted-foreground/50 h-4 w-4" />
                              )}
                            </span>
                          )}
                        </div>
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  onClick={() => onRowClick?.(row)}
                  className={cn(onRowClick && "hover:bg-accent cursor-pointer")}
                >
                  {row.getVisibleCells().map((cell, index) => {
                    const isOutput =
                      outputColumnStartIndex !== undefined &&
                      index >= outputColumnStartIndex;
                    const isFirstOutput =
                      outputColumnStartIndex !== undefined &&
                      index === outputColumnStartIndex;
                    return (
                      <TableCell
                        key={cell.id}
                        isOutput={isOutput}
                        isFirstOutput={isFirstOutput}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export function StatusBadge({ status }: { status: string }) {
  if (status.toLowerCase() === "paid") {
    return (
      <div className="flex items-center">
        <span className="bg-success/10 text-success flex h-6 w-6 items-center justify-center rounded-full">
          <CheckCircleIcon className="h-4 w-4" />
        </span>
      </div>
    );
  }

  return <span>{status}</span>;
}

export function CustomerCell({
  name,
  email,
}: {
  name: string;
  email: string;
  avatarSrc?: string;
}) {
  return (
    <div className="flex items-center gap-3">
      <div className="flex flex-col">
        <span className="font-medium">{name}</span>
        <span className="text-muted-foreground text-xs">{email}</span>
      </div>
    </div>
  );
}

export function FilterChip({
  label,
  value,
  onRemove,
}: {
  label: string;
  value: string;
  onRemove: () => void;
}) {
  return (
    <div className="filter-pill">
      <span>
        {label} {value}
      </span>
      <button
        onClick={onRemove}
        className="hover:bg-secondary-foreground/10 rounded-full"
      >
        <XIcon className="h-4 w-4" />
      </button>
    </div>
  );
}

export function FilterDialog({
  onApply,
  onCancel,
  open,
  children,
}: {
  onApply: () => void;
  onCancel: () => void;
  open: boolean;
  children: React.ReactNode;
}) {
  if (!open) return null;

  return (
    <div className="bg-popover text-popover-foreground border-border absolute top-full right-0 z-50 mt-2 w-72 rounded-md border p-4 shadow-md">
      {children}
      <div className="mt-4 flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button className="filter-button" onClick={onApply}>
          Apply Filter
        </Button>
      </div>
    </div>
  );
}
