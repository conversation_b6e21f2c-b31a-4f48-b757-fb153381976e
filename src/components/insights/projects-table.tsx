"use client";

import { useRouter } from "next/navigation";
import { Badge } from "~/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { cn } from "~/lib/utils";

interface Project {
  id: string;
  name: string;
  symbol: string;
  category: string;
  marketCap: string;
  fdv: string;
  insiderPercentage: number;
  publicPercentage: number;
}

const projects: Project[] = [
  {
    id: "1",
    name: "Ethereum",
    symbol: "ETH",
    category: "Layer 1",
    marketCap: "$400B",
    fdv: "$400B",
    insiderPercentage: 15,
    publicPercentage: 70,
  },
  {
    id: "2",
    name: "Arbitrum",
    symbol: "ARB",
    category: "Layer 2",
    marketCap: "$2.1B",
    fdv: "$8.5B",
    insiderPercentage: 26.94,
    publicPercentage: 11.62,
  },
  {
    id: "3",
    name: "Optimism",
    symbol: "OP",
    category: "Layer 2",
    marketCap: "$2.8B",
    fdv: "$8.6B",
    insiderPercentage: 25,
    publicPercentage: 5,
  },
  {
    id: "4",
    name: "Polygon",
    symbol: "MATIC",
    category: "Layer 2",
    marketCap: "$4.8B",
    fdv: "$9.2B",
    insiderPercentage: 23,
    publicPercentage: 19,
  },
  {
    id: "5",
    name: "Avalanche",
    symbol: "AVAX",
    category: "Layer 1",
    marketCap: "$15.2B",
    fdv: "$28.8B",
    insiderPercentage: 42,
    publicPercentage: 10,
  },
  {
    id: "6",
    name: "Solana",
    symbol: "SOL",
    category: "Layer 1",
    marketCap: "$110B",
    fdv: "$140B",
    insiderPercentage: 48.86,
    publicPercentage: 1.64,
  },
  {
    id: "7",
    name: "Chainlink",
    symbol: "LINK",
    category: "Oracle",
    marketCap: "$8.9B",
    fdv: "$15.2B",
    insiderPercentage: 30,
    publicPercentage: 35,
  },
  {
    id: "8",
    name: "Uniswap",
    symbol: "UNI",
    category: "DeFi",
    marketCap: "$8.2B",
    fdv: "$12.1B",
    insiderPercentage: 40,
    publicPercentage: 60,
  },
];

const categoryColors: Record<string, string> = {
  "Layer 1": "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  "Layer 2":
    "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
  Oracle:
    "bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300",
  DeFi: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
};

export function ProjectsTable() {
  const router = useRouter();

  const handleRowClick = (projectId: string) => {
    router.push(`/insights/${projectId}`);
  };

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader className="sticky top-0 z-20">
          <TableRow>
            <TableHead className="w-[200px]">
              <div className="flex items-center gap-1">Project</div>
            </TableHead>
            <TableHead>Category</TableHead>
            <TableHead>
              <div className="flex items-center gap-1">Market Cap</div>
            </TableHead>
            <TableHead>
              <div className="flex items-center gap-1">Circulating Supply</div>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {projects.map((project) => (
            <TableRow
              key={project.id}
              className="cursor-pointer"
              onClick={() => handleRowClick(project.id)}
            >
              <TableCell>
                <div>
                  <div className="hover:text-primary font-medium transition-colors">
                    {project.name}
                  </div>
                  <div className="text-muted-foreground text-sm">
                    {project.symbol}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge
                  variant="secondary"
                  className={cn(
                    "font-normal",
                    categoryColors[project.category]
                  )}
                >
                  {project.category}
                </Badge>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">{project.marketCap}</div>
                  <div className="text-muted-foreground text-sm">
                    FDV: {project.fdv}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-red-500" />
                  <span className="font-medium">
                    {project.insiderPercentage}%
                  </span>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
